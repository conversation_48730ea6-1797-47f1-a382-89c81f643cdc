defmodule EventsServiceWeb.SeatCommentGroupController do
  @moduledoc false

  use EventsServiceWeb, :controller
  use OpenApiSpex.ControllerSpecs

  alias EventsService.SeatComments
  alias EventsService.SeatComments.SeatCommentGroup
  alias EventsService.SeatComments.SeatCommentGroupObject
  alias EventsService.Util.UUIDHelper
  alias EventsServiceWeb.ApiSchemas.SeatCommentSchema.SeatCommentCreateResponse
  alias EventsServiceWeb.ApiSchemas.SeatCommentSchema.SeatCommentGroupResponse
  alias EventsServiceWeb.ApiSchemas.SeatCommentSchema.SeatCommentGroupsResponse
  alias EventsServiceWeb.ChangesetJSON
  alias EventsServiceWeb.Plugs.Authorize
  alias OpenApiSpex.Reference

  require Logger

  action_fallback EventsServiceWeb.FallbackController

  plug Authorize, [rule: ["promoter", "event", "read"], permission: "event.view"] when action in [:index]

  plug Authorize,
       [rule: ["promoter", "event", "write"], permission: "event.edit"] when action in [:create, :update, :delete]

  tags ["Seats.io Comments"]

  operation :index,
    summary: "List all seat comment groups",
    parameters: [
      id: [
        in: :path,
        description: "Event ID",
        type: :string,
        example: "500c94b2-830a-4b27-b043-625c1a754af5"
      ]
    ],
    responses: %{
      :ok => {"Seat comment group response", "application/json", SeatCommentGroupResponse},
      :bad_request => %Reference{"$ref": "#/components/responses/bad_request"},
      :not_found => %Reference{"$ref": "#/components/responses/not_found"},
      :internal_server_error => %Reference{"$ref": "#/components/responses/internal_server_error"}
    }

  def index(%{assigns: %{current_user_id: _user_id}} = conn, %{"event_id" => event_id} = params) do
    if event_access?(conn, event_id, "event.view") do
      seat_comment_groups = SeatCommentGroup.get_all_for_event_id(event_id, params)

      conn
      |> put_status(:ok)
      |> render(:index, seat_comment_groups: seat_comment_groups)
    else
      conn
      |> put_status(:forbidden)
      |> json(%{message: "Insufficient privileges to access this resource", error_code: :insufficient_privileges})
    end
  end

  operation :show,
    summary: "List all seat comment groups for specific group key",
    parameters: [
      id: [
        in: :path,
        description: "Event ID",
        type: :string,
        example: "500c94b2-830a-4b27-b043-625c1a754af5"
      ],
      group_key: [
        in: :path,
        description: "Group key",
        type: :string,
        example: "ff55942a-ad69-5a5f-8d6a-4e916dc4b8d3"
      ]
    ],
    responses: %{
      :ok => {"Seat comment group response", "application/json", SeatCommentGroupsResponse},
      :bad_request => %Reference{"$ref": "#/components/responses/bad_request"},
      :not_found => %Reference{"$ref": "#/components/responses/not_found"},
      :internal_server_error => %Reference{"$ref": "#/components/responses/internal_server_error"}
    }

  def show(%{assigns: %{current_user_id: _user_id}} = conn, %{"event_id" => event_id, "group_key" => group_key}) do
    with {_, true} <- {:access_validation, event_access?(conn, event_id, "event.view")},
         {_, %SeatCommentGroup{} = seat_comment_group} <-
           {:get_seat_comment_group,
            SeatCommentGroup.get_by([event_id: event_id, group_key: group_key], [:seat_comment_group_objects])} do
      conn |> put_status(:ok) |> render(:show, seat_comment_group: seat_comment_group)
    else
      {:access_validation, false} ->
        conn
        |> put_status(:forbidden)
        |> json(%{message: "Insufficient privileges to access this resource", error_code: :insufficient_privileges})

      {:get_seat_comment_group, nil} ->
        conn
        |> put_status(:not_found)
        |> json(%{message: "Seat comment group not found.", error_code: :not_found})
    end
  end

  operation :create,
    summary: "Create seat comment group",
    parameters: [
      id: [
        in: :path,
        description: "Event ID",
        type: :string,
        example: "500c94b2-830a-4b27-b043-625c1a754af5"
      ],
      object_ids: [
        in: :query,
        required: true,
        description: "The selected seats.io object IDs",
        type: :array,
        example: ["test123-P-1"]
      ],
      name: [
        in: :query,
        description: "Group name",
        type: :string,
        example: "Red Fig"
      ],
      group_key: [
        in: :query,
        description: "Group key",
        type: :string,
        example: "ff55942a-ad69-5a5f-8d6a-4e916dc4b8d3"
      ]
    ],
    responses: %{
      :created => {"Seat comment group response", "application/json", SeatCommentCreateResponse},
      :bad_request => %Reference{"$ref": "#/components/responses/bad_request"},
      :not_found => %Reference{"$ref": "#/components/responses/not_found"},
      :internal_server_error => %Reference{"$ref": "#/components/responses/internal_server_error"}
    }

  def create(%{assigns: %{current_user_id: user_id}} = conn, %{"event_id" => event_id} = params) do
    created_by_field = UUIDHelper.get_created_by_field(user_id)

    params =
      params
      |> Map.put("event_id", event_id)
      |> Map.put(Atom.to_string(created_by_field), user_id)

    with {_, true} <- {:access_validation, event_access?(conn, event_id, "event.edit")},
         {_, {:ok, %{seat_comment_group: seat_comment_group} = res}} <-
           {:create_seat_comment_group, SeatComments.create_seat_comment_group(params)} do
      seat_comment_group_objects =
        Enum.flat_map(res, fn
          {{:seat_comment_group_object, _id}, object} ->
            [object]

          _ ->
            []
        end)

      seat_comment_group = %{seat_comment_group | seat_comment_group_objects: seat_comment_group_objects}

      conn
      |> put_status(:created)
      |> render(:show, seat_comment_group: seat_comment_group)
    else
      {:access_validation, false} ->
        conn
        |> put_status(:forbidden)
        |> json(%{message: "Insufficient privileges to access this resource", error_code: :insufficient_privileges})

      {:create_seat_comment_group, {:error, failed_operation, failed_value, _changes_so_far}} ->
        Logger.error(
          "Failed to create seat comment group with in step #{failed_operation} with error: #{inspect(failed_value)}"
        )

        conn
        |> put_status(:unprocessable_entity)
        |> json(%{
          message: "Failed to create seat comment group.",
          error_code: :create_seat_comment_group_error
        })
    end
  end

  operation :update,
    summary: "Update seat comment group",
    parameters: [
      id: [
        in: :path,
        description: "Event ID",
        type: :string,
        example: "500c94b2-830a-4b27-b043-625c1a754af5"
      ],
      name: [
        in: :query,
        description: "Group name",
        type: :string,
        example: "Blue Fig"
      ]
    ],
    responses: %{
      :ok => {"Seat comment group response", "application/json", SeatCommentCreateResponse},
      :bad_request => %Reference{"$ref": "#/components/responses/bad_request"},
      :not_found => %Reference{"$ref": "#/components/responses/not_found"},
      :internal_server_error => %Reference{"$ref": "#/components/responses/internal_server_error"}
    }

  def update(%{assigns: %{current_user_id: _user_id}} = conn, %{"id" => id} = params) do
    with {_, %{event_id: event_id} = seat_comment_group} <- {:get_seat_comment_group, SeatCommentGroup.get(id)},
         {_, true} <- {:access_validation, event_access?(conn, event_id, "event.edit")},
         {_, {:ok, seat_comment_group}} <-
           {:update_seat_comment_group, SeatCommentGroup.update(seat_comment_group, params)} do
      seat_comment_group_objects =
        SeatCommentGroupObject.get_all_by_group_id(seat_comment_group.id)

      seat_comment_group = %{seat_comment_group | seat_comment_group_objects: seat_comment_group_objects}

      conn
      |> put_status(:ok)
      |> render(:show, seat_comment_group: seat_comment_group)
    else
      {:get_seat_comment_group, nil} ->
        conn
        |> put_status(:not_found)
        |> json(%{message: "Seat comment group not found.", error_code: :not_found})

      {:access_validation, false} ->
        conn
        |> put_status(:forbidden)
        |> json(%{message: "Insufficient privileges to access this resource", error_code: :insufficient_privileges})

      {:update_seat_comment_group, {:error, failed_value}} ->
        Logger.error("Failed to update seat comment group with error: #{ChangesetJSON.error_to_text(failed_value)}")

        conn
        |> put_status(:unprocessable_entity)
        |> json(%{
          message: "Failed to update seat comment group.",
          error_code: :update_seat_comment_group_error
        })
    end
  end

  operation :delete,
    summary: "Delete seat comment group",
    parameters: [
      id: [
        in: :path,
        description: "Event ID",
        type: :string,
        example: "500c94b2-830a-4b27-b043-625c1a754af5"
      ],
      group_key: [
        in: :path,
        description: "Group key",
        type: :string,
        example: "ff55942a-ad69-5a5f-8d6a-4e916dc4b8d3"
      ]
    ],
    responses: %{
      :no_content => %Reference{"$ref": "#/components/responses/no_content"},
      :bad_request => %Reference{"$ref": "#/components/responses/bad_request"},
      :not_found => %Reference{"$ref": "#/components/responses/not_found"},
      :internal_server_error => %Reference{"$ref": "#/components/responses/internal_server_error"}
    }

  def delete(%{assigns: %{current_user_id: _user_id}} = conn, %{"event_id" => event_id, "group_key" => group_key}) do
    with {_, true} <- {:access_validation, event_access?(conn, event_id, "event.edit")},
         {_, %SeatCommentGroup{} = seat_comment_group} <-
           {:get_seat_comment_group,
            SeatCommentGroup.get_by([group_key: group_key, event_id: event_id], [
              :seat_comment_group_objects,
              :seat_comments
            ])},
         {_, {:ok, _seat_comment_group}} <-
           {:delete_seat_comment_group, SeatComments.delete_seat_comment_group(seat_comment_group)} do
      conn
      |> put_status(:no_content)
      |> text("")
    else
      {:access_validation, false} ->
        conn
        |> put_status(:forbidden)
        |> json(%{message: "Insufficient privileges to access this resource", error_code: :insufficient_privileges})

      {:get_seat_comment_group, nil} ->
        conn
        |> put_status(:not_found)
        |> json(%{message: "Not found", error_code: :not_found})

      {:delete_seat_comment_group, {:error, failed_operation, failed_value, _changes_so_far}} ->
        Logger.error(
          "Failed to delete seat comment group in step #{failed_operation} with error: #{inspect(failed_value)}"
        )

        conn
        |> put_status(:unprocessable_entity)
        |> json(%{
          message: "Failed to delete seat comment group.",
          error_code: :delete_seat_comment_group_error
        })
    end
  end
end
