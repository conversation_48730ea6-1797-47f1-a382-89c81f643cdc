defmodule EventsServiceWeb.Services.TrackingPixelJSON do
  alias EventsService.Tracking.TrackingPixel
  alias EventsServiceWeb.Services.TrackingPixelCredentialJSON

  @doc """
  Renders a list of tracking pixels.
  """
  def index(%{tracking_pixels: tracking_pixels}) do
    for(tracking_pixel <- tracking_pixels, do: data(tracking_pixel))
  end

  @doc """
  Renders a single tracking pixel.
  """
  def show(%{tracking_pixel: tracking_pixel}) do
    data(tracking_pixel)
  end

  defp data(%TrackingPixel{} = tracking_pixel) do
    %{
      id: tracking_pixel.id,
      eventId: tracking_pixel.event_id,
      label: tracking_pixel.label,
      type: tracking_pixel.type,
      trackingPixelCredentials: maybe_transform_tracking_pixel_credentials(tracking_pixel)
    }
  end

  defp maybe_transform_tracking_pixel_credentials(%TrackingPixel{tracking_pixel_credentials: tracking_pixel_credentials})
       when is_list(tracking_pixel_credentials) do
    TrackingPixelCredentialJSON.index(%{tracking_pixel_credentials: tracking_pixel_credentials})
  end

  defp maybe_transform_tracking_pixel_credentials(_event), do: nil
end
