defmodule EventsServiceWeb.SeatCommentController do
  @moduledoc false

  use EventsServiceWeb, :controller
  use OpenApiSpex.ControllerSpecs

  alias EventsService.SeatComments.SeatComment
  alias EventsService.SeatComments.SeatCommentGroup
  alias EventsService.Util.UUIDHelper
  alias EventsServiceWeb.ApiSchemas.SeatCommentSchema.SeatCommentCreateResponse
  alias EventsServiceWeb.ApiSchemas.SeatCommentSchema.SeatCommentGroupResponse
  alias EventsServiceWeb.ApiSchemas.SeatCommentSchema.SeatCommentsResponse
  alias EventsServiceWeb.ChangesetJSON
  alias EventsServiceWeb.Plugs.Authorize
  alias ExServiceClient.Services.AccountsService
  alias OpenApiSpex.Reference

  require Logger

  action_fallback EventsServiceWeb.FallbackController

  plug Authorize,
       [rule: ["promoter", "event", "read"], permission: "event.view"]
       when action in [:index]

  plug Authorize,
       [rule: ["promoter", "event", "write"], permission: "event.edit"]
       when action in [:create, :delete]

  tags ["Seats.io Comments"]

  operation :index,
    summary: "Get seat comments for group_keys or object_ids",
    parameters: [
      id: [
        in: :path,
        description: "Event ID",
        type: :string,
        example: "500c94b2-830a-4b27-b043-625c1a754af5"
      ],
      group_keys: [
        in: :query,
        description: "Group key",
        type: :array,
        example: ["ff55942a-ad69-5a5f-8d6a-4e916dc4b8d3", "ff55942a-ad69-5a5f-8d6a-4e916dc4b8d4"]
      ],
      object_ids: [
        in: :query,
        description: "The selected seats.io object IDs",
        type: :array,
        example: ["test123-P-1", "test123-P-2"]
      ]
    ],
    responses: %{
      :ok => {"Seat comments response", "application/json", SeatCommentsResponse},
      :bad_request => %Reference{"$ref": "#/components/responses/bad_request"},
      :not_found => %Reference{"$ref": "#/components/responses/not_found"},
      :internal_server_error => %Reference{"$ref": "#/components/responses/internal_server_error"}
    }

  def index(%{assigns: %{current_user_id: _user_id}} = conn, %{"event_id" => event_id} = params) do
    if event_access?(conn, event_id, "event.view") do
      seat_comments = SeatComment.get_latest(event_id, params)

      entries =
        seat_comments.entries
        |> get_user_ids_for_missing_authors()
        |> get_user_infos()
        |> merge_user_info(seat_comments.entries)

      conn
      |> put_status(:ok)
      |> render(:index, seat_comments: %{seat_comments | entries: entries})
    else
      conn
      |> put_status(:forbidden)
      |> json(%{message: "Insufficient privileges to access this resource", error_code: :insufficient_privileges})
    end
  end

  operation :create,
    summary: "Create new seat comment",
    parameters: [
      id: [
        in: :path,
        description: "Event ID",
        type: :string,
        example: "500c94b2-830a-4b27-b043-625c1a754af5"
      ],
      group_key: [
        in: :path,
        description: "Group key",
        type: :string,
        example: "ff55942a-ad69-5a5f-8d6a-4e916dc4b8d3"
      ]
    ],
    request_body: {"Seat comment create request", "application/json", SeatCommentCreateResponse},
    responses: %{
      :created => {"Seat comment response", "application/json", SeatCommentGroupResponse},
      :bad_request => %Reference{"$ref": "#/components/responses/bad_request"},
      :not_found => %Reference{"$ref": "#/components/responses/not_found"},
      :internal_server_error => %Reference{"$ref": "#/components/responses/internal_server_error"}
    }

  def create(
        %{assigns: %{current_user_id: user_id}} = conn,
        %{"event_id" => event_id, "group_key" => group_key} = params
      ) do
    history_id = params["history_id"] || UUID.uuid4()
    created_by_field = UUIDHelper.get_created_by_field(user_id)

    params =
      params
      |> Map.put("event_id", event_id)
      |> Map.put(Atom.to_string(created_by_field), user_id)
      |> Map.put("history_id", history_id)

    with {_, true} <- {:access_validation, event_access?(conn, event_id, "event.edit")},
         {_, %{id: group_id, event_id: ^event_id}} <-
           {:get_seat_comment_group, SeatCommentGroup.get_by(event_id: event_id, group_key: group_key)},
         {_, {:ok, seat_comment}} <-
           {:create_seat_comment, SeatComment.create(Map.put(params, "seat_comment_group_id", group_id))} do
      conn
      |> put_status(:created)
      |> render(:show, seat_comment: seat_comment)
    else
      {:access_validation, false} ->
        conn
        |> put_status(:forbidden)
        |> json(%{message: "Insufficient privileges to access this resource", error_code: :insufficient_privileges})

      {:get_seat_comment_group, nil} ->
        conn
        |> put_status(:not_found)
        |> json(%{message: "Seat comment group not found", error_code: :not_found})

      {:create_seat_comment, {:error, changeset}} ->
        Logger.error("Failed to create seat comment with error: #{inspect(ChangesetJSON.error_to_text(changeset))}")

        conn
        |> put_status(:unprocessable_entity)
        |> json(%{
          message: "Failed to create seat comment.",
          error_code: :create_seat_comment_error
        })
    end
  end

  operation :delete,
    summary: "Delete seat comment",
    parameters: [
      id: [
        in: :path,
        description: "Event ID",
        type: :string,
        example: "500c94b2-830a-4b27-b043-625c1a754af5"
      ],
      comment: [
        in: :path,
        description: "Seat comment",
        type: :object,
        example: SeatComment
      ]
    ],
    responses: %{
      :no_content => %Reference{"$ref": "#/components/responses/no_content"},
      :bad_request => %Reference{"$ref": "#/components/responses/bad_request"},
      :not_found => %Reference{"$ref": "#/components/responses/not_found"},
      :internal_server_error => %Reference{"$ref": "#/components/responses/internal_server_error"}
    }

  def delete(%{assigns: %{current_user_id: _user_id}} = conn, %{"id" => id}) do
    with {_, %{seat_comment_group: %{event_id: event_id}} = seat_comment} <-
           {:get_seat_comment_group, SeatComment.get(id, [:seat_comment_group])},
         {_, true} <- {:access_validation, event_access?(conn, event_id, "event.edit")} do
      _seat_comment_group = SeatComment.delete(seat_comment)

      conn
      |> put_status(:no_content)
      |> text("")
    else
      {:access_validation, false} ->
        conn
        |> put_status(:forbidden)
        |> json(%{message: "Insufficient privileges to access this resource", error_code: :insufficient_privileges})

      {:get_seat_comment_group, nil} ->
        conn
        |> put_status(:not_found)
        |> json(%{message: "Seat comment not found", error_code: :not_found})
    end
  end

  operation :history,
    summary: "Get seat comments history",
    parameters: [
      id: [
        in: :path,
        description: "Event ID",
        type: :string,
        example: "500c94b2-830a-4b27-b043-625c1a754af5"
      ],
      history_id: [
        in: :path,
        description: "History ID",
        type: :string,
        example: "500c94b2-830a-4b27-b043-625c1a754af5"
      ]
    ],
    responses: %{
      :ok => {"Seat comments response", "application/json", SeatCommentsResponse},
      :bad_request => %Reference{"$ref": "#/components/responses/bad_request"},
      :not_found => %Reference{"$ref": "#/components/responses/not_found"},
      :internal_server_error => %Reference{"$ref": "#/components/responses/internal_server_error"}
    }

  def history(%{assigns: %{current_user_id: _user_id}} = conn, %{"event_id" => event_id, "history_id" => history_id}) do
    if event_access?(conn, event_id, "event.view") do
      seat_comments = SeatComment.get_by_history_id(event_id, history_id)

      entries =
        seat_comments.entries
        |> get_user_ids_for_missing_authors()
        |> get_user_infos()
        |> merge_user_info(seat_comments.entries)

      conn
      |> put_status(:ok)
      |> render(:index, seat_comments: %{seat_comments | entries: entries})
    else
      conn
      |> put_status(:forbidden)
      |> json(%{message: "Insufficient privileges to access this resource", error_code: :insufficient_privileges})
    end
  end

  defp get_user_ids_for_missing_authors(entries) do
    entries
    |> Enum.reduce([], fn %{seat_comment_author: author}, acc ->
      if is_author_a_promoter?(author) do
        []
      else
        [author.user_id | acc]
      end
    end)
    |> Enum.uniq()
  end

  defp get_user_infos(user_ids) do
    Map.new(user_ids, fn user_id ->
      case AccountsService.get_user_by_id(user_id) do
        {:ok, user} ->
          {user_id, %{given_name: user["firstName"], family_name: user["lastName"]}}

        {:error, _} ->
          Logger.error("Failed to fetch user info for user_id: #{user_id}")
          {user_id, %{}}
      end
    end)
  end

  defp is_author_a_promoter?(author) do
    name = author.display_name || author.given_name || author.family_name || author.company_name

    case name do
      nil -> false
      _ -> true
    end
  end

  defp merge_user_info(user_infos, entries) do
    Enum.map(entries, fn %{seat_comment_author: author} = entry ->
      case Map.get(user_infos, author.user_id) do
        nil -> entry
        user_info -> Map.put(entry, :seat_comment_author, Map.merge(author, user_info))
      end
    end)
  end
end
