defmodule ExServiceClient.Services.ShortlinkService.LinkRequests do
  @moduledoc """
  HTTP client for making requests to the shortlink service.
  
  This module handles the low-level HTTP communication with the shortlink service
  and provides functions for creating links.
  """

  use ExServiceClient.Clients.BaseClient

  alias ExServiceClient.Services.ShortlinkService.Link

  @opts [endpoint: :shortlink]

  @doc """
  Creates a new shortlink by making a POST request to the shortlink service.

  ## Parameters
  - `params` - A map containing the link creation parameters. Expected keys:
    - `"target_url"` or `:target_url` - The URL to redirect to
    - `"shortlink"` or `:shortlink` - The desired short URL

  ## Returns
  - `{:ok, %Link{}}` - On successful creation
  - `{:error, %{message: term(), status: integer()}}` - On HTTP error
  - `{:error, term()}` - On other errors

  ## Examples

      iex> LinkRequests.create_link(%{"target_url" => "https://example.com", "shortlink" => "https://short.ly/abc123"})
      {:ok, %Link{id: "123", srcUrl: "https://short.ly/abc123", targetUrl: "https://example.com", ...}}

  """
  @spec create_link(map()) :: {:ok, Link.t()} | {:error, term()}
  def create_link(params) when is_map(params) do
    # Normalize the parameter keys to match the API expectations
    normalized_params = normalize_params(params)

    @opts
    |> client()
    |> Tesla.post("/links", normalized_params)
    |> parse_response(&decode_link/1)
  end

  defp normalize_params(params) do
    %{
      "targetUrl" => get_param(params, ["target_url", :target_url, "targetUrl", :targetUrl]),
      "srcUrl" => get_param(params, ["shortlink", :shortlink, "srcUrl", :srcUrl])
    }
  end

  defp get_param(params, keys) do
    Enum.find_value(keys, fn key -> Map.get(params, key) end)
  end

  defp decode_link(body) when is_map(body) do
    Poison.Decoder.decode(struct(Link), body, [])
  end
  defp decode_link(body), do: body
end
