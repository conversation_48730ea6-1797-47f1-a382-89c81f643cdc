defmodule ExServiceClient.Services.ShortlinkService do
  @moduledoc """
  Public facade for the shortlink service.
  
  This module provides the main interface for interacting with the shortlink service,
  delegating to the appropriate request modules while maintaining a clean public API.
  """

  alias ExServiceClient.Services.ShortlinkService.LinkRequests

  @doc """
  Creates a new shortlink.

  This function delegates to the LinkRequests module to handle the actual HTTP request
  to the shortlink service.

  ## Parameters
  - `params` - A map containing the link creation parameters. Expected keys:
    - `"target_url"` or `:target_url` - The URL to redirect to
    - `"shortlink"` or `:shortlink` - The desired short URL

  ## Returns
  - `{:ok, %Link{}}` - On successful creation
  - `{:error, %{message: term(), status: integer()}}` - On HTTP error
  - `{:error, term()}` - On other errors

  ## Examples

      iex> ShortlinkService.create_link(%{"target_url" => "https://example.com", "shortlink" => "https://short.ly/abc123"})
      {:ok, %Link{id: "123", srcUrl: "https://short.ly/abc123", targetUrl: "https://example.com", ...}}

  """
  @spec create_link(map()) :: {:ok, ExServiceClient.Services.ShortlinkService.Link.t()} | {:error, term()}
  defdelegate create_link(params), to: LinkRequests
end
