defmodule ExServiceClient.Services.ShortlinkService.Link do
  @moduledoc """
  Represents a link response from the shortlink service.
  
  This struct contains the fields returned by the shortlink service API
  when creating or retrieving links.
  """

  @derive [Poison.Encoder]
  defstruct [
    :id,
    :srcUrl,
    :targetUrl,
    :insertedAt,
    :updatedAt
  ]

  @type t :: %__MODULE__{
          id: String.t(),
          srcUrl: String.t(),
          targetUrl: String.t(),
          insertedAt: DateTime.t(),
          updatedAt: DateTime.t()
        }
end

defimpl Poison.Decoder, for: ExServiceClient.Services.ShortlinkService.Link do
  @doc """
  Decodes a JSON response into a Link struct, parsing ISO8601 datetime strings
  into Elixir DateTime structs.
  """
  def decode(value, _options) do
    %ExServiceClient.Services.ShortlinkService.Link{
      id: value["id"],
      srcUrl: value["srcUrl"],
      targetUrl: value["targetUrl"],
      insertedAt: parse_datetime(value["insertedAt"]),
      updatedAt: parse_datetime(value["updatedAt"])
    }
  end

  defp parse_datetime(nil), do: nil
  defp parse_datetime(datetime_string) when is_binary(datetime_string) do
    case DateTime.from_iso8601(datetime_string) do
      {:ok, datetime, _offset} -> datetime
      {:error, _reason} -> nil
    end
  end
  defp parse_datetime(_), do: nil
end
