defmodule EventsService.SeatComments.SeatComment do
  @moduledoc false

  use Ecto.Schema

  import Ecto.Changeset
  import Ecto.Query

  alias EventsService.Repo
  alias EventsService.SeatComments.SeatComment
  alias EventsService.Vendor.Promoter

  @schema_prefix :events
  @primary_key {:id, :binary_id, autogenerate: true}
  @foreign_key_type :binary_id
  schema "seat_comments" do
    field :created_by, :binary_id
    field :created_by_document_id, :string
    field :history_id, :binary_id
    field :text, :string

    field :deleted_at, :utc_datetime
    belongs_to :seat_comment_group, EventsService.SeatComments.SeatCommentGroup

    timestamps()
  end

  def changeset(seat_comment, attrs) do
    seat_comment
    |> cast(attrs, [
      :seat_comment_group_id,
      :created_by,
      :created_by_document_id,
      :history_id,
      :text
    ])
    |> validate_required([
      :seat_comment_group_id,
      :history_id,
      :text
    ])
  end

  def delete_changeset(seat_comment, attrs) do
    seat_comment
    |> cast(attrs, [
      :deleted_at
    ])
    |> validate_required([
      :deleted_at
    ])
  end

  def create(attrs) do
    %SeatComment{}
    |> SeatComment.changeset(attrs)
    |> Repo.insert()
  end

  def delete(%SeatComment{history_id: history_id} = _seat_comment) do
    now = DateTime.utc_now()

    Repo.update_all(from(sc in SeatComment, where: sc.history_id == ^history_id),
      set: [deleted_at: now, updated_at: now]
    )
  end

  @spec get_latest(binary()) :: Scrivener.Page.t()
  @spec get_latest(binary(), keyword() | map()) :: Scrivener.Page.t()
  def get_latest(event_id, params \\ %{}) do
    {order_by, order_dir} = extract_order_params(params)

    latest_comments_query =
      event_id
      |> build_latest_comments_query(params)
      |> apply_group_keys_filter(params["group_keys"])
      |> apply_object_ids_filter(params["object_ids"])

    history_count_subquery = build_history_count_subquery()

    query =
      from(sc in SeatComment,
        inner_join: sq in subquery(latest_comments_query),
        as: :latest_comments,
        on: sc.id == sq.comment_id,
        inner_join: hc in subquery(history_count_subquery),
        as: :history_count,
        on: sc.history_id == hc.history_id,
        order_by: [{^order_dir, ^order_by}],
        preload: [:seat_comment_group],
        select: %{
          seat_comment: sc,
          seat_comment_author: sq,
          history_count: hc.count
        }
      )

    Repo.paginate(query, params)
  end

  def get(id, preloads \\ []) do
    query =
      from sc in SeatComment,
        where: sc.id == ^id,
        where: is_nil(sc.deleted_at),
        preload: ^preloads

    Repo.one(query)
  end

  def get_by_history_id(event_id, history_id, params \\ %{}) do
    order_by = params["order_by"] || String.to_existing_atom("inserted_at")
    order_dir = params["order_dir"] || String.to_existing_atom("desc")

    latest_comments =
      from(sc in SeatComment,
        inner_join: scg in assoc(sc, :seat_comment_group),
        as: :seat_comment_group,
        inner_join: scgo in assoc(scg, :seat_comment_group_objects),
        as: :seat_comment_group_objects,
        left_join: p in Promoter,
        as: :promoter,
        on:
          (not is_nil(sc.created_by_document_id) and sc.created_by_document_id == p.created_by_document_id) or
            (not is_nil(sc.created_by) and sc.created_by == p.created_by),
        where: scg.event_id == ^event_id,
        where: sc.history_id == ^history_id,
        where: is_nil(sc.deleted_at),
        where: is_nil(scg.deleted_at),
        where: is_nil(scgo.deleted_at),
        order_by: [desc: sc.inserted_at],
        distinct: sc.id,
        select: %{
          comment_id: sc.id,
          group_key: scg.group_key,
          user_id: fragment("COALESCE(?, ?)", sc.created_by_document_id, sc.created_by),
          display_name: p.display_name,
          given_name: p.given_name,
          family_name: p.family_name,
          company_name: p.company_name
        }
      )

    query =
      from(sc in SeatComment,
        inner_join: sq in subquery(latest_comments),
        as: :latest_comments,
        on: sc.id == sq.comment_id,
        order_by: [{^order_dir, ^order_by}],
        preload: [:seat_comment_group],
        select: %{
          seat_comment: sc,
          seat_comment_author: sq
        }
      )

    Repo.paginate(query, params)
  end

  defp extract_order_params(params) do
    order_by = params["order_by"] || String.to_existing_atom("inserted_at")
    order_dir = params["order_dir"] || String.to_existing_atom("desc")
    {order_by, order_dir}
  end

  defp build_latest_comments_query(event_id, _params) do
    from(sc in SeatComment,
      inner_join: scg in assoc(sc, :seat_comment_group),
      as: :seat_comment_group,
      inner_join: scgo in assoc(scg, :seat_comment_group_objects),
      as: :seat_comment_group_objects,
      left_join: p in Promoter,
      as: :promoter,
      on:
        (not is_nil(sc.created_by_document_id) and sc.created_by_document_id == p.created_by_document_id) or
          (not is_nil(sc.created_by) and sc.created_by == p.created_by),
      where: scg.event_id == ^event_id,
      where: is_nil(sc.deleted_at),
      where: is_nil(scg.deleted_at),
      where: is_nil(scgo.deleted_at),
      order_by: [desc: sc.inserted_at],
      distinct: sc.history_id,
      select: %{
        comment_id: sc.id,
        group_key: scg.group_key,
        user_id: fragment("COALESCE(?, ?)", sc.created_by_document_id, sc.created_by),
        display_name: p.display_name,
        given_name: p.given_name,
        family_name: p.family_name,
        company_name: p.company_name
      }
    )
  end

  defp build_history_count_subquery do
    from(sc in SeatComment,
      group_by: sc.history_id,
      select: %{
        history_id: sc.history_id,
        count: count(sc.id)
      }
    )
  end

  defp apply_group_keys_filter(query, nil), do: query

  defp apply_group_keys_filter(query, group_keys) do
    where(query, [seat_comment_group: scg], scg.group_key in ^group_keys)
  end

  defp apply_object_ids_filter(query, nil), do: query

  defp apply_object_ids_filter(query, object_ids) do
    where(query, [seat_comment_group_objects: scgo], scgo.object_id in ^object_ids)
  end
end
