defmodule EventsService.UserMigrator do
  @moduledoc """
  This module is responsible for migrating user IDs from document IDs in the database.

  It is going to be removed once all document IDs are migrated to user IDs.
  """

  import Ecto.Query

  alias Ecto.Multi
  alias EventsService.Events.Event
  alias EventsService.Repo
  alias ExServiceClient.Services.AccountsService

  require Logger

  @tables_to_migrate [
    {EventsService.Events.EntranceArea, :user_document_id, :user_id, nil},
    {EventsService.Events.EntranceAreaValidationPermission, :user_document_id, :user_id, nil},
    {Event, :created_by_document_id, :created_by, nil},
    {Event, :closed_by_document_id, :closed_by, nil},
    {EventsService.Events.EventPermission, :user_document_id, :user_id, nil},
    {EventsService.Guestlists.InvitationApprovalRequest, :user_document_id, :user_id, nil},
    {EventsService.Guestlists.InvitationHistory, :actor_document_id, :actor_id, [actor_type: "USER"]},
    {EventsService.Guestlists.PersonalInformation, :user_document_id, :user_id, nil},
    {EventsService.SeatComments.SeatCommentGroup, :created_by_document_id, :created_by, nil},
    {EventsService.SeatComments.SeatComment, :created_by_document_id, :created_by, nil},
    {EventsService.Vendor.Promoter, :created_by_document_id, :created_by, nil}
  ]

  @spec migrate :: list()
  def migrate do
    Logger.info("Starting user ID migration for #{Enum.count(@tables_to_migrate)} tables...")

    for {schema, doc_id_field, user_id_field, where_clause} <- @tables_to_migrate do
      migrate_table(schema, doc_id_field, user_id_field, where_clause)
    end
  end

  @spec revert :: list()
  def revert do
    Logger.info("Starting user ID revert for #{Enum.count(@tables_to_migrate)} tables...")

    for {schema, doc_id_field, user_id_field, where_clause} <- @tables_to_migrate do
      revert_table(schema, doc_id_field, user_id_field, where_clause)
    end
  end

  defp migrate_table(schema, doc_id_field, user_id_field, where_clause) do
    Logger.info("Migrating user IDs for #{inspect(schema)}...")

    query =
      from(e in schema,
        where: not is_nil(field(e, ^doc_id_field)),
        where: is_nil(field(e, ^user_id_field)),
        select: {e.id, field(e, ^doc_id_field)}
      )

    query =
      if where_clause do
        Enum.reduce(where_clause, query, fn {field, value}, q ->
          where(q, [e], field(e, ^field) == ^value)
        end)
      else
        query
      end

    Repo.transaction(fn ->
      query
      |> Repo.stream()
      |> Stream.chunk_every(100)
      |> Enum.each(fn chunk ->
        process_chunk(chunk, schema, doc_id_field, user_id_field)
      end)
    end)

    Logger.info("Finished migrating #{inspect(schema)}.")
  end

  defp revert_table(schema, doc_id_field, user_id_field, where_clause) do
    Logger.info("Reverting user IDs for #{inspect(schema)}...")

    query =
      from(e in schema,
        where: not is_nil(field(e, ^user_id_field)),
        where: not is_nil(field(e, ^doc_id_field)),
        select: e.id
      )

    query =
      if where_clause do
        Enum.reduce(where_clause, query, fn {field, value}, q ->
          where(q, [e], field(e, ^field) == ^value)
        end)
      else
        query
      end

    Repo.transaction(fn ->
      query
      |> Repo.stream()
      |> Stream.chunk_every(100)
      |> Enum.each(fn chunk ->
        record_ids = Enum.map(chunk, & &1)
        update_query = from(e in schema, where: e.id in ^record_ids)
        {updated_count, _} = Repo.update_all(update_query, set: [{user_id_field, nil}])
        Logger.info("Successfully reverted #{updated_count} records in chunk.")
      end)
    end)

    Logger.info("Finished reverting #{inspect(schema)}.")
  end

  defp process_chunk(chunk, schema, _doc_id_field, user_id_field) do
    doc_id_to_record_ids =
      Enum.reduce(chunk, %{}, fn {record_id, doc_id}, acc ->
        Map.update(acc, doc_id, [record_id], fn existing -> [record_id | existing] end)
      end)

    doc_ids = Map.keys(doc_id_to_record_ids)

    user_id_map =
      doc_ids
      |> Task.async_stream(&fetch_user_mapping/1, max_concurrency: 10)
      |> Enum.reduce(%{}, fn
        {:ok, {doc_id, user_id}}, acc when not is_nil(user_id) -> Map.put(acc, doc_id, user_id)
        _, acc -> acc
      end)

    multi =
      Enum.reduce(user_id_map, Multi.new(), fn {doc_id, user_id}, multi_acc ->
        record_ids = Map.get(doc_id_to_record_ids, doc_id)
        update_query = from(e in schema, where: e.id in ^record_ids)

        Multi.update_all(multi_acc, "update_#{doc_id}", update_query, set: [{user_id_field, user_id}])
      end)

    case Repo.transaction(multi) do
      {:ok, results} ->
        updated_count =
          results
          |> Map.values()
          |> Enum.map(fn {count, _} -> count end)
          |> Enum.sum()

        Logger.info("Successfully updated #{updated_count} records.")

      {:error, failed_op, failed_value, _} ->
        Logger.error("Error updating batch. Operation: #{failed_op}, Reason: #{inspect(failed_value)}")
    end
  end

  defp fetch_user_mapping(doc_id) do
    case AccountsService.get_user_by_id(doc_id) do
      {:ok, %{"id" => user_id}} ->
        {doc_id, user_id}

      {:error, _} ->
        Logger.warning("User not found in AccountsService for document_id: #{doc_id}")
        {doc_id, nil}
    end
  end
end
